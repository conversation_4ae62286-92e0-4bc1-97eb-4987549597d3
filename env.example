# =============================================================================
# FLASHLOAN ARBITRAGE BOT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# NEVER commit your .env file to version control!

# =============================================================================
# BLOCKCHAIN CONNECTION
# =============================================================================

# Your wallet private key (KEEP THIS SECRET!)
# Generate a new wallet for this bot - don't use your main wallet
PRIVATE_KEY=0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# RPC Endpoint - Use WebSocket for real-time data
# Alchemy (recommended for production)
ALCHEMY_API_KEY=your_alchemy_api_key_here
RPC_URL=wss://eth-mainnet.ws.alchemyapi.io/v2/your_alchemy_api_key_here

# Alternative: Infura
# INFURA_API_KEY=your_infura_api_key_here
# RPC_URL=wss://mainnet.infura.io/ws/v3/your_infura_api_key_here

# =============================================================================
# SMART CONTRACT
# =============================================================================

# Deployed FlashloanArbitrage contract address
CONTRACT_ADDRESS=0x1234....90

# =============================================================================
# TRADING PARAMETERS
# =============================================================================

# Minimum profit in USD to execute arbitrage
MIN_PROFIT_USD=50

# Maximum gas price in Gwei (to avoid high gas costs)
MAX_GAS_PRICE_GWEI=100

# Slippage tolerance percentage (0.5 = 0.5%)
SLIPPAGE_TOLERANCE=0.5

# =============================================================================
# API KEYS (Optional but recommended)
# =============================================================================

# Etherscan API key for contract verification
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# CoinMarketCap API key for gas reporting
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here

# =============================================================================
# ADVANCED SETTINGS (Optional)
# =============================================================================

# Custom flashloan amounts (in wei for ETH, in token units for others)
# FLASHLOAN_AMOUNT_WETH=10000000000000000000
# FLASHLOAN_AMOUNT_USDC=50000000000
# FLASHLOAN_AMOUNT_USDT=50000000000
# FLASHLOAN_AMOUNT_DAI=50000000000000000000000

# Execution cooldown in milliseconds (prevent spam)
# EXECUTION_COOLDOWN=30000

# Price data expiry in milliseconds
# PRICE_DATA_EXPIRY=30000

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable gas reporting
REPORT_GAS=false

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

# Network to deploy/run on (mainnet, goerli, sepolia)
NETWORK=mainnet

# =============================================================================
# MEV PROTECTION (Advanced - Optional)
# =============================================================================

# Flashbots relay endpoint (for MEV protection)
# FLASHBOTS_RELAY_URL=https://relay.flashbots.net

# Private RPC endpoint (for front-run protection)
# PRIVATE_RPC_URL=your_private_rpc_endpoint

# =============================================================================
# SAFETY SETTINGS
# =============================================================================

# Maximum ETH balance to risk (safety limit)
MAX_ETH_BALANCE=10

# Emergency stop flag (set to true to stop bot)
EMERGENCY_STOP=false

# =============================================================================
# EXAMPLE VALUES FOR TESTING
# =============================================================================
# For testing on Goerli testnet:
# NETWORK=goerli
# RPC_URL=wss://eth-goerli.ws.alchemyapi.io/v2/your_alchemy_api_key_here
# MIN_PROFIT_USD=1
# MAX_GAS_PRICE_GWEI=50
