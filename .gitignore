# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Hardhat files
cache/
reports/
artifacts/
typechain-types/

# Deployment files
.openzeppelin/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Hardhat specific
cache/
artifacts/
typechain/
typechain-types/

# Test files
test-results/
coverage/

# Build files
dist/
build/

# Temporary files
tmp/
temp/

# Private keys and sensitive data
*.key
*.pem
private-keys/
secrets/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak

# Lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
