{"ethereum": {"name": "Ethereum Mainnet", "chainId": 1, "rpcUrl": "https://eth-mainnet.g.alchemy.com/v2/", "explorerUrl": "https://etherscan.io", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"UNISWAP_V2": "******************************************", "UNISWAP_V3": "******************************************", "SUSHISWAP": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************", "USDT": "******************************************", "DAI": "******************************************"}, "priceFeeds": {"ETH_USD": "******************************************", "USDC_USD": "******************************************", "USDT_USD": "******************************************", "DAI_USD": "******************************************"}}, "sepolia": {"name": "Sepolia Testnet", "chainId": 11155111, "rpcUrl": "https://eth-sepolia.g.alchemy.com/v2/", "explorerUrl": "https://sepolia.etherscan.io", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"UNISWAP_V2": "******************************************", "UNISWAP_V3": "******************************************", "UNISWAP_V3_FACTORY": "******************************************", "UNISWAP_V2_FACTORY": "******************************************", "SUSHISWAP": "******************************************", "SUSHISWAP_FACTORY": "******************************************", "BALANCER_VAULT": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************", "DAI": "******************************************", "USDT": "******************************************"}, "priceFeeds": {"ETH_USD": "******************************************", "BTC_USD": "******************************************", "USDC_USD": "******************************************", "DAI_USD": "******************************************"}}, "polygon": {"name": "Polygon", "chainId": 137, "rpcUrl": "https://polygon-mainnet.g.alchemy.com/v2/", "explorerUrl": "https://polygonscan.com", "nativeCurrency": {"name": "MATIC", "symbol": "MATIC", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"UNISWAP_V3": "******************************************", "SUSHISWAP": "******************************************", "QUICKSWAP": "******************************************"}, "tokens": {"WMATIC": "******************************************", "USDC": "******************************************", "USDT": "******************************************", "DAI": "******************************************"}}, "bsc": {"name": "BNB Smart Chain", "chainId": 56, "rpcUrl": "https://bsc-dataseed1.binance.org/", "explorerUrl": "https://bscscan.com", "nativeCurrency": {"name": "BNB", "symbol": "BNB", "decimals": 18}, "aaveAddressProvider": "0x6807dc923806fE8Fd134338EABCA509979a7e0cB", "dexRouters": {"PANCAKESWAP_V2": "0x10ED43C718714eb63d5aA57B78B54704E256024E", "PANCAKESWAP_V3": "0x13f4EA83D0bd40E75C8222255bc855a974568Dd4", "SUSHISWAP": "******************************************"}, "tokens": {"WBNB": "0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c", "USDC": "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d", "USDT": "0x55d398326f99059fF775485246999027B3197955", "BUSD": "******************************************"}, "priceFeeds": {"BNB_USD": "******************************************", "USDC_USD": "******************************************", "USDT_USD": "******************************************", "BUSD_USD": "******************************************"}}, "arbitrum": {"name": "Arbitrum One", "chainId": 42161, "rpcUrl": "https://arb-mainnet.g.alchemy.com/v2/", "explorerUrl": "https://arbiscan.io", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"UNISWAP_V3": "******************************************", "SUSHISWAP": "******************************************", "CAMELOT": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************", "USDT": "******************************************", "ARB": "******************************************"}}, "optimism": {"name": "Optimism", "chainId": 10, "rpcUrl": "https://opt-mainnet.g.alchemy.com/v2/", "explorerUrl": "https://optimistic.etherscan.io", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"UNISWAP_V3": "******************************************", "SUSHISWAP": "******************************************", "VELODROME": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************", "USDT": "******************************************", "OP": "******************************************"}}, "avalanche": {"name": "Avalanche C-Chain", "chainId": 43114, "rpcUrl": "https://api.avax.network/ext/bc/C/rpc", "explorerUrl": "https://snowtrace.io", "nativeCurrency": {"name": "AVAX", "symbol": "AVAX", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"TRADERJOE": "******************************************", "PANGOLIN": "******************************************", "SUSHISWAP": "******************************************"}, "tokens": {"WAVAX": "******************************************", "USDC": "******************************************", "USDT": "******************************************", "JOE": "******************************************"}}, "fantom": {"name": "Fantom Opera", "chainId": 250, "rpcUrl": "https://rpc.ftm.tools/", "explorerUrl": "https://ftmscan.com", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "FTM", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"SPOOKYSWAP": "0xF491e7B69E4244ad4002BC14e878a34207E38c29", "SPIRITSWAP": "******************************************", "SUSHISWAP": "******************************************"}, "tokens": {"WFTM": "******************************************", "USDC": "******************************************", "USDT": "******************************************", "BOO": "******************************************"}}, "base": {"name": "Base", "chainId": 8453, "rpcUrl": "https://mainnet.base.org", "explorerUrl": "https://basescan.org", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": "******************************************", "dexRouters": {"UNISWAP_V3": "******************************************", "SUSHISWAP": "******************************************", "BASESWAP": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************", "DAI": "******************************************"}}, "linea": {"name": "Linea", "chainId": 59144, "rpcUrl": "https://rpc.linea.build", "explorerUrl": "https://lineascan.build", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": null, "dexRouters": {"LINEABANK": "******************************************", "SUSHISWAP": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************"}}, "scroll": {"name": "<PERSON><PERSON>", "chainId": 534352, "rpcUrl": "https://rpc.scroll.io", "explorerUrl": "https://scrollscan.com", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": null, "dexRouters": {"SCROLLSWAP": "******************************************", "SUSHISWAP": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************"}}, "zksync": {"name": "zkSync Era", "chainId": 324, "rpcUrl": "https://mainnet.era.zksync.io", "explorerUrl": "https://explorer.zksync.io", "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "aaveAddressProvider": null, "dexRouters": {"SYNCSWAP": "******************************************", "MUTE": "******************************************"}, "tokens": {"WETH": "******************************************", "USDC": "******************************************"}}, "mantle": {"name": "Mantle", "chainId": 5000, "rpcUrl": "https://rpc.mantle.xyz", "explorerUrl": "https://explorer.mantle.xyz", "nativeCurrency": {"name": "Mantle", "symbol": "MNT", "decimals": 18}, "aaveAddressProvider": null, "dexRouters": {"FUSIONX": "******************************************", "AGNI": "******************************************"}, "tokens": {"WMNT": "******************************************", "USDC": "******************************************", "USDT": "******************************************"}}}